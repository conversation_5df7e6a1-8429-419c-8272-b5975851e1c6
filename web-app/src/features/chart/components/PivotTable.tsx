import { useMemo, useCallback } from 'react';
import { ComputedCell, ResponsiveHeatMap } from '@nivo/heatmap';
import { Box, Typography } from '@mui/material';
import { StatsKeyValue, StatsGroupBy } from '../../stats/statsTypes';
import { getValueBasedColor, randomThemeColor, truncate } from '../chartFunctions';
import { ValueColorRange } from '../chartTypes';
import useAxisMargin, { AxisOrientation } from '../hooks/useAxisMargin';

interface TooltipProps {
  cell: {
    serieId: string;
    data: { x: string };
    formattedValue: string | null;
  };
}

function HeatmapTooltip({ cell }: TooltipProps) {
  return (
    <Box
      sx={{
        background: 'white',
        padding: '9px 12px',
        border: '1px solid #ccc',
        borderRadius: '4px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
      }}
    >
      <Typography variant="body2">
        <strong>{cell.serieId}</strong> × <strong>{cell.data.x}</strong>
      </Typography>
      <Typography variant="body2" color="primary">
        Count: <strong>{cell.formattedValue || '0'}</strong>
      </Typography>
    </Box>
  );
}

interface PivotTableProps {
  data: StatsKeyValue;
  fieldGroupByNamingMapping: Record<string, string>;
  fieldStackedByNamingMapping: Record<string, string>;
  groupBy: StatsGroupBy;
  stackedBy: StatsGroupBy;
  valueColorRanges?: ValueColorRange[];
}

interface PivotData {
  rows: string[];
  columns: string[];
  values: Record<string, Record<string, number>>;
  totals: {
    rowTotals: Record<string, number>;
    columnTotals: Record<string, number>;
    grandTotal: number;
  };
  maxValue: number;
}

function PivotTable({
  data,
  fieldGroupByNamingMapping,
  fieldStackedByNamingMapping,
  groupBy,
  stackedBy,
  valueColorRanges,
}: PivotTableProps) {
  const defaultMargin = 8;
  const topMargin = defaultMargin;
  const rightMargin = defaultMargin + 5;
  const truncateLabelsAt = 25;

  const pivotData = useMemo((): PivotData => {
    const rows = new Set<string>();
    const columns = new Set<string>();
    const values: Record<string, Record<string, number>> = {};
    const rowTotals: Record<string, number> = {};
    const columnTotals: Record<string, number> = {};
    let grandTotal = 0;
    let maxValue = 0;

    // Process the nested data structure
    Object.entries(data).forEach(([rowKey, rowValue]) => {
      if (typeof rowValue === 'object' && !('count' in rowValue)) {
        rows.add(rowKey);
        if (!values[rowKey]) values[rowKey] = {};
        if (!rowTotals[rowKey]) rowTotals[rowKey] = 0;

        Object.entries(rowValue).forEach(([colKey, colValue]) => {
          if (typeof colValue === 'object' && 'count' in colValue) {
            columns.add(colKey);
            const { count } = colValue as { count: number };
            values[rowKey][colKey] = count;
            rowTotals[rowKey] += count;
            columnTotals[colKey] = (columnTotals[colKey] || 0) + count;
            grandTotal += count;
            maxValue = Math.max(maxValue, count);
          }
        });
      }
    });

    return {
      rows: Array.from(rows).sort(),
      columns: Array.from(columns).sort(),
      values,
      totals: { rowTotals, columnTotals, grandTotal },
      maxValue,
    };
  }, [data]);

  const getDisplayName = useCallback(
    (key: string, type: 'row' | 'column') => {
      if (type === 'row') {
        return fieldGroupByNamingMapping[key];
      }
      return fieldStackedByNamingMapping[key];
    },
    [fieldGroupByNamingMapping, fieldStackedByNamingMapping]
  );

  const longestKey = useMemo(() => {
      const keys = pivotData.rows.map((row) => getDisplayName(row, 'row'));
      return keys.reduce(
        (a, b) => (truncate(a, truncateLabelsAt).length > truncate(b, truncateLabelsAt).length ? a : b),
        ''
      );
    },
    [pivotData.rows, getDisplayName]
  );

  const longestValue = useMemo(() => {
    const values = pivotData.columns.map((col) => getDisplayName(col, 'column'));
    return values.reduce(
      (a, b) => (truncate(a, truncateLabelsAt).length > truncate(b, truncateLabelsAt).length ? a : b),
      ''
    );
  }, [pivotData.columns, getDisplayName]);

  const heatmapData = useMemo(
    () =>
      pivotData.rows.map((row) => ({
        id: getDisplayName(row, 'row'),
        data: pivotData.columns.map((col) => ({
          x: getDisplayName(col, 'column'),
          y: pivotData.values[row]?.[col] || 0,
        })),
      })),
    [pivotData, getDisplayName]
  );

  const valueColorCache = useMemo(() => new Map<number, string>(), []);

  const getColor = useCallback(
    (cell: Omit<ComputedCell<{ x: string; y: number }>, 'borderColor' | 'color' | 'opacity' | 'labelTextColor'>) => {
      if (!cell.value) return '#3182ce';

      if (valueColorRanges && valueColorRanges.length > 0) {
        const color = getValueBasedColor(cell.value, valueColorRanges);
        if (color) return color;
      }

      // Use cached random color or generate new one
      if (!valueColorCache.has(cell.value)) {
        valueColorCache.set(cell.value, randomThemeColor());
      }
      return valueColorCache.get(cell.value) || '#3182ce';
    },
    [valueColorRanges, valueColorCache]
  );

  const {
    margin: bottomMargin,
    axisRef: bottomAxisRef,
    adjustedAxisOptions: bottomAxisOptions,
  } = useAxisMargin({
    axisOptions: {
      tickRotation: -45,
      truncateTickAt: truncateLabelsAt,
    },
    orientation: AxisOrientation.HORIZONTAL,
    axisDefaultMargin: defaultMargin,
  });
  const {
    margin: leftMargin,
    axisRef: leftAxisRef,
    adjustedAxisOptions: leftAxisOptions,
  } = useAxisMargin({
    axisOptions: {
      truncateTickAt: truncateLabelsAt,
    },
    orientation: AxisOrientation.VERTICAL,
    axisDefaultMargin: defaultMargin,
  });

  if (pivotData.rows.length === 0 || pivotData.columns.length === 0) {
    return (
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Typography color="text.secondary">No data available for pivot table</Typography>
      </Box>
    );
  }

  return (
    <>
      <span
        ref={leftAxisRef}
        style={{
          position: 'absolute',
          visibility: 'hidden',
          pointerEvents: 'none',
          whiteSpace: 'pre',
          fontFamily: 'sans-serif',
          fontSize: '11px',
          fontWeight: 400,
        }}
      >
        {longestKey}
      </span>
      <span
        ref={bottomAxisRef}
        style={{
          position: 'absolute',
          visibility: 'hidden',
          pointerEvents: 'none',
          whiteSpace: 'pre',
          fontFamily: 'sans-serif',
          fontSize: '11px',
          fontWeight: 400,
        }}
      >
        {longestValue}
      </span>
      <Box sx={{ height: '100%', position: 'relative' }}>
      <ResponsiveHeatMap
        data={heatmapData}
        margin={{ top: topMargin, right: rightMargin, bottom: bottomMargin, left: leftMargin }}
        valueFormat=">-.0f"
        axisTop={null}
        axisRight={null}
        axisBottom={bottomAxisOptions}
        axisLeft={leftAxisOptions}
        borderColor={{
          from: 'color',
          modifiers: [['darker', 0.4]],
        }}
        labelTextColor={{
          from: 'color',
          modifiers: [['darker', 1.8]],
        }}
        animate
        motionConfig="wobbly"
        tooltip={HeatmapTooltip}
        colors={getColor}
      />
    </Box>
    </>
  );
}

export default PivotTable;
