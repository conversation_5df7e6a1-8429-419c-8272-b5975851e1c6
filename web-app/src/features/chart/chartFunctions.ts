import dayjs from 'dayjs';
import { ChartConfigurationFormInput, ChartSetting, paletteColors, RollingPeriod, ValueColorRange } from './chartTypes';
import { themeToColor } from '../../theme';
import { hasMetaMap, KeyTotal, StatsGroupBy, StatsGroupByFieldMetaMap, StatsKeyValue } from '../stats/statsTypes';

export default function getDateRangeFromRollingPeriod(period: RollingPeriod): { startDate: number; endDate: number } {
  const end = dayjs().endOf('day');
  let start: dayjs.Dayjs;

  switch (period) {
    case RollingPeriod.LAST_7_DAYS:
      start = end.subtract(7, 'days').startOf('day');
      break;
    case RollingPeriod.LAST_30_DAYS:
      start = end.subtract(30, 'days').startOf('day');
      break;
    case RollingPeriod.LAST_90_DAYS:
      start = end.subtract(90, 'days').startOf('day');
      break;
    case RollingPeriod.LAST_6_MONTHS:
      start = end.subtract(6, 'months').startOf('day');
      break;
    case RollingPeriod.LAST_YEAR:
      start = end.subtract(1, 'year').startOf('day');
      break;
    default:
      start = end.subtract(7, 'days').startOf('day');
  }

  return {
    startDate: start.valueOf(),
    endDate: end.valueOf(),
  };
}

export function randomThemeColor(): string {
  return themeToColor(paletteColors[Math.floor(Math.random() * paletteColors.length)]);
}

/**
 * Extracts all numerical count values from statsData, assuming at most two levels:
 *  • statsData[row].count
 *  • statsData[row][col].count
 * @param statsData – an object whose values are either { count: number } or an object mapping to { count: number }
 * @returns a flat array of every count found (skips anything that isn’t a number)
 */
export function getValuesFromStatsData(statsData: StatsKeyValue): number[] {
  const values: number[] = [];

  Object.values(statsData).forEach((entry) => {
    if (entry && typeof entry === 'object' && 'count' in entry && typeof entry.count === 'number') {
      values.push(entry.count);
    } else if (entry && typeof entry === 'object') {
      Object.values(entry).forEach((subEntry) => {
        if (subEntry && typeof subEntry === 'object' && 'count' in subEntry && typeof subEntry.count === 'number') {
          values.push(subEntry.count);
        }
      });
    }
  });

  return values.filter((v) => !Number.isNaN(v));
}

export function getMaxValueFromStatsData(statsData: StatsKeyValue): number {
  const values = getValuesFromStatsData(statsData);
  return values.length > 0 ? Math.max(...values) : 0;
}

/**
 * Get a color for a key based on various color mapping strategies
 *
 * @param key The key to get a color for
 * @param fieldColorMapping Optional user-defined mapping of fields to colors
 * @param groupBy Optional grouping parameter to look up predefined colors from metadata
 * @returns The color for the key - either from fieldColorMapping, metadata, or a random theme color
 */
export function getColor(key: string, fieldColorMapping?: Record<string, string>, groupBy?: StatsGroupBy): string {
  if (fieldColorMapping?.[key]) {
    return fieldColorMapping[key];
  }

  if (!groupBy || !hasMetaMap(groupBy)) {
    return randomThemeColor();
  }

  const metaValue = StatsGroupByFieldMetaMap[groupBy][key];
  if (!metaValue) {
    return randomThemeColor();
  }

  return metaValue.color;
}
/**
 * Get a display name for a key based on metadata mapping
 *
 * @param key The key to get a display name for
 * @param groupBy Optional grouping parameter to look up predefined names from metadata
 * @returns The display name for the key - either from metadata or the original key
 */
export function getFieldName(key: string, groupBy?: StatsGroupBy): string {
  if (!groupBy || !hasMetaMap(groupBy)) {
    return key;
  }

  const metaValue = StatsGroupByFieldMetaMap[groupBy][key];
  if (!metaValue) {
    return key;
  }

  return metaValue.label;
}

// helper to elide with an ellipsis:
export const truncate = (s: string, max: number) => (max > 0 && s.length > max ? `${s.slice(0, max)}…` : s);

// ? Helper to extract the value for a given key
export const extractY = (total: number | KeyTotal[], key: string) => {
  const items = Array.isArray(total) ? total : [{ key, total }];
  return items.find((item) => item.key === key)?.total ?? 0;
};

export const convertChartSettingToForm = (chartSetting: ChartSetting): ChartConfigurationFormInput => ({
  ...chartSetting,
});

export const convertFormToChartSetting = (form: ChartConfigurationFormInput): ChartSetting => {
  const { fieldArrayId, ...rest } = form;
  const entries = Object.entries(rest).filter(([, v]) => v != null);
  return Object.fromEntries(entries) as unknown as ChartSetting;
};

/**
 * Sorts an array of ValueColorRange objects by their minimum values in ascending order
 *
 * @param ranges - Array of ValueColorRange objects to sort
 * @returns A new sorted array of ValueColorRange objects
 */
function sortColorRanges(ranges: ValueColorRange[]): ValueColorRange[] {
  return [...ranges].sort((a, b) => a.minValue - b.minValue);
}

/**
 * Determines the appropriate color for a numeric value based on defined color ranges
 *
 * @param value - The numeric value to find a color for
 * @param ranges - Array of ValueColorRange objects defining the color mapping rules
 * @returns The matching color string if found, null otherwise
 * @example
 * const ranges = [
 *   { minValue: 0, maxValue: 10, color: 'red' },
 *   { minValue: 11, maxValue: 20, color: 'blue' }
 * ];
 * getValueBasedColor(5, ranges); // returns 'red'
 */
export function getValueBasedColor(value: number, ranges: ValueColorRange[]): string | null {
  if (!ranges || ranges.length === 0) {
    return null;
  }

  const sortedRanges = sortColorRanges(ranges);

  const matchingRange = sortedRanges.find((range) => value >= range.minValue && value <= range.maxValue);

  if (matchingRange) {
    return matchingRange.color;
  }

  return null;
}

/**
 * Validates that color ranges don't overlap and are properly ordered
 * @param ranges - Array of ValueColorRange objects to validate
 * @returns Error message string if validation fails, null if validation passes
 * @description
 * Performs two validations:
 * 1. Ensures each range's minimum value is less than its maximum value
 * 2. Checks that ranges don't overlap with adjacent ranges
 */
export function validateColorRanges(ranges: ValueColorRange[]): string | null {
  if (ranges.length === 0) return null;

  const sortedRanges = [...ranges].sort((a, b) => a.minValue - b.minValue);

  for (let i = 0; i < sortedRanges.length; i += 1) {
    const range = sortedRanges[i];

    if (range.minValue >= range.maxValue) {
      return `Range ${i + 1}: Minimum value must be less than maximum value`;
    }

    if (i < sortedRanges.length - 1) {
      const nextRange = sortedRanges[i + 1];
      if (range.maxValue > nextRange.minValue) {
        return `Ranges ${i + 1} and ${i + 2} overlap`;
      }
    }
  }

  return null;
}

/**
 * Analyzes numerical data distribution to identify patterns and characteristics
 * @param values - Array of numerical values to analyze
 * @returns Object containing analysis results:
 *   - uniqueValues: Sorted array of unique values
 *   - frequencies: Map of value frequencies
 *   - hasZeros: Boolean indicating presence of zero values
 *   - nonZeroValues: Array of unique non-zero values
 */
function analyzeDataDistribution(values: number[]): {
  uniqueValues: number[];
  frequencies: Map<number, number>;
  hasZeros: boolean;
  nonZeroValues: number[];
} {
  const frequencies = new Map<number, number>();
  values.forEach((value) => {
    frequencies.set(value, (frequencies.get(value) || 0) + 1);
  });

  const uniqueValues = [...frequencies.keys()].sort((a, b) => a - b);
  const hasZeros = uniqueValues.includes(0);
  const nonZeroValues = uniqueValues.filter((v) => v > 0);

  return { uniqueValues, frequencies, hasZeros, nonZeroValues };
}

/**
 * Creates meaningful numerical thresholds based on data distribution analysis
 * @param analysis - Result of analyzeDataDistribution function
 * @returns Array of threshold values that represent natural breakpoints in the data
 * @description
 * Handles several cases:
 * 1. Empty data or only zeros: returns [1]
 * 2. Few unique values (≤4): uses actual values as breakpoints
 * 3. Complex distributions: identifies natural clusters and uses cluster boundaries
 */
function createDataDrivenThresholds(analysis: ReturnType<typeof analyzeDataDistribution>): number[] {
  const { nonZeroValues } = analysis;

  if (nonZeroValues.length === 0) {
    return [1];
  }

  if (nonZeroValues.length <= 4) {
    const thresholds = [...nonZeroValues];
    const maxValue = Math.max(...nonZeroValues);
    if (maxValue <= 10) {
      thresholds.push(maxValue + 1);
    } else {
      thresholds.push(Math.ceil(maxValue * 1.1));
    }
    return thresholds;
  }

  const sortedValues = [...nonZeroValues].sort((a, b) => a - b);
  const clusters: number[][] = [];
  let currentCluster = [sortedValues[0]];

  for (let i = 1; i < sortedValues.length; i += 1) {
    const gap = sortedValues[i] - sortedValues[i - 1];
    const avgValue = Math.ceil((sortedValues[i] + sortedValues[i - 1]) / 2);

    if (gap > Math.max(1, avgValue * 0.5)) {
      clusters.push(currentCluster);
      currentCluster = [sortedValues[i]];
    } else {
      currentCluster.push(sortedValues[i]);
    }
  }
  clusters.push(currentCluster);

  const thresholds: number[] = [];

  for (let i = 0; i < clusters.length; i += 1) {
    const cluster = clusters[i];
    const maxInCluster = Math.max(...cluster);

    if (i === clusters.length - 1) {
      thresholds.push(Math.ceil(maxInCluster + 1));
    } else {
      thresholds.push(Math.ceil(maxInCluster));
    }
  }

  return thresholds;
}

/**
 * Automatically generates color ranges based on the distribution of numerical values
 * @param values - Array of numerical values to analyze
 * @returns Array of ValueColorRange objects defining color mappings
 * @description
 * Handles special cases:
 * 1. Empty data: returns empty array
 * 2. Only zeros: creates single range [0,1]
 * 3. Single non-zero value: creates 1-2 ranges depending on presence of zeros
 * 4. Multiple values: creates ranges based on natural data clusters
 * Each range is assigned a color from the palette cyclically
 */
export function generateAutoColorRanges(values: number[]): ValueColorRange[] {
  if (values.length === 0) {
    return [];
  }

  const analysis = analyzeDataDistribution(values);
  const { hasZeros, nonZeroValues } = analysis;

  if (nonZeroValues.length === 0) {
    return [
      {
        minValue: 0,
        maxValue: 1,
        color: themeToColor(paletteColors[0]),
      },
    ];
  }

  // ? Handle edge case: all values are the same non-zero value
  if (nonZeroValues.length === 1) {
    const singleValue = nonZeroValues[0];
    const ranges: ValueColorRange[] = [];

    if (hasZeros) {
      ranges.push({
        minValue: 0,
        maxValue: singleValue,
        color: themeToColor(paletteColors[0]),
      });
    }

    ranges.push({
      minValue: hasZeros ? singleValue : 0,
      maxValue: singleValue + 1,
      color: themeToColor(paletteColors[hasZeros ? 1 : 0]),
    });

    return ranges;
  }

  const thresholds = createDataDrivenThresholds(analysis);

  const ranges: ValueColorRange[] = [];
  let currentMin = hasZeros ? 0 : Math.min(...nonZeroValues);

  thresholds.forEach((threshold: number, index: number) => {
    const colorIndex = index % paletteColors.length;
    ranges.push({
      minValue: currentMin,
      maxValue: threshold,
      color: themeToColor(paletteColors[colorIndex]),
    });
    currentMin = threshold;
  });

  return ranges.filter((range) => range.maxValue > range.minValue);
}
